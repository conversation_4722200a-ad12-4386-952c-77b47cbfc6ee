# ArcSAR雷达监测系统设计文档

**版本：3.0** **最后更新：2025年8月12日**

---

## **项目概述**

ArcSAR雷达监测系统是一个基于Flask的分布式雷达数据采集与分析平台，专门用于地面形变监测、散射图像处理和动目标检测。系统采用TCP长连接与雷达设备通信，支持多雷达并发管理，提供完整的Web API接口和用户权限管理功能。

### **核心特性**

- **多雷达并发管理**：支持数百台雷达设备同时在线，采用线程安全的设备管理机制
- **实时数据处理**：支持散射图像、形变数据、置信度数据和动目标数据的实时接收与处理
- **场景化管理**：基于地理场景的设备分组管理，支持复杂的监测任务配置
- **高性能数据存储**：采用MongoDB分片存储，支持TIFF格式图像数据和元数据管理
- **分层日志架构**：模块化日志系统，支持API、雷达核心、服务器等不同模块的独立日志管理

---

## **1. 系统角色与权限划分**

### **1.1 基础角色定义**

| **角色**   | **权限描述**                                                                                                                                                  | **数量限制** |
| ---------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------ |
| **管理员** | - 全局支配权：所有数据、账号、场景绑定的最高控制权 - 账号管理：创建/删除/禁用/启用用户 - 权限调整：直接升降级用户角色 - 系统监控和运维权限 - 雷达设备注册与分配管理 | 建议3-5个          |
| **用户**   | - 场景访问：可访问被授权的场景数据 - 数据查看：查看雷达监测数据、图像和分析结果 - 基础操作：场景坐标设置、监测区域管理 - 报告查阅：查看监测报告和统计数据           | 无数量限制         |

### **1.2 权限细分与API访问控制**

**管理员权限细分：**

- **雷达设备管理**：雷达注册、删除、状态监控、参数配置
- **场景管理**：场景创建、删除、重命名、坐标设置
- **任务管理**：监测任务创建、修改、删除、重命名
- **用户管理**：用户注册、密码重置、角色分配
- **系统监控**：日志查看、性能监控、异常处理
- **数据管理**：数据备份、清理、导出、归档

**用户权限细分：**

- **数据查看权限**：查看场景监测数据、雷达状态、图像数据
- **场景操作权限**：场景坐标设置、监测区域添加/删除
- **参数查询权限**：查询雷达场景参数、设备信息
- **数据分析权限**：访问数据分析接口、生成统计报告
- **文件下载权限**：下载图像文件、监测数据、分析结果

### **1.3 JWT认证与会话管理**

**认证机制：**

- 基于JWT Token的无状态认证
- Token有效期：访问令牌1天，刷新令牌30天
- 支持开发环境的测试Token（GET /user/login）
- 密码采用BCrypt哈希加密，随机盐值生成

**会话安全：**

- 自动Token过期处理和刷新机制
- 统一的错误响应格式（TOKEN_EXPIRED, INVALID_TOKEN, UNAUTHORIZED）
- API接口级别的权限验证装饰器

---

## **2. 雷达设备与场景管理**

### **2.1 雷达设备架构**

**设备通信协议：**

- 基于TCP长连接的自定义二进制协议
- 支持命令码和扩展码的双层指令体系
- 实现心跳机制（30秒间隔）和自动重连
- 数据传输支持MD5校验和Snappy压缩

**设备管理机制：**

- 单例模式的RadarManager统一管理所有雷达实例
- 线程安全的设备注册/注销机制
- 支持设备动态上线和离线检测
- 每个雷达设备拥有独立的MongoDB数据库

### **2.2 场景生命周期管理**

**场景创建与配置：**

- 管理员通过Web API创建场景（/radar_manage/change_scene_name）
- 场景包含名称、地理坐标、背景包、关联雷达列表
- 支持场景重命名和坐标动态调整
- 场景与雷达设备多对多关联关系

**场景数据结构：**

```json
{
  "_id": "ObjectId",
  "name": "场景名称",
  "background_pack": "背景数据包路径",
  "coordinates": [经度, 纬度, 高度],
  "radar_ID": ["雷达ID列表"]
}
```

### **2.3 雷达设备注册与状态管理**

**自动注册流程：**

- 雷达设备首次连接时发送设备ID（8位十六进制）
- 系统自动在base_data.radar集合中创建设备记录
- 为每个雷达创建独立数据库和文件目录结构
- 从Excel配置文件导入雷达信息、场景参数、平台命令

**设备状态字段：**

```json
{
  "ID": "雷达设备ID",
  "name": "设备名称",
  "is_online": 1,  // 在线状态：0离线，1在线
  "is_work": 0,    // 工作状态：0停止，1工作中
  "mission_ID": [], // 任务列表
  "radar_coordinates": [], // 雷达坐标
  "scene": "关联场景ID"
}
```

**故障检测与恢复：**

- 心跳超时检测（连续3次失败标记为故障）
- 自动重连机制（每5分钟尝试重连）
- 设备本地数据缓存（24小时）和恢复后同步
- 实时状态更新和告警通知

### **2.4 监测任务与区域管理**

**任务生命周期：**

- 任务自动创建：雷达开始工作时根据当前时间自动生成任务
- 任务命名规则：YYYY-MM-DD HH:MM格式的时间戳
- 任务数据隔离：每个任务拥有独立的数据集合
- 任务管理API：支持重命名、删除等操作

**任务数据结构：**

```json
{
  "ID": "任务ID",
  "name": "任务名称",
  "coordinates": [经度, 纬度, 高度],
  "创建时间": "自动生成"
}
```

**监测区域管理：**

- 矩形区域定义：westLon, southLat, eastLon, northLat
- 区域标签和描述信息
- 按任务分组的区域数据存储（monitor_area_{mission_id}）
- 支持区域的添加、删除、查询操作

**场景参数配置：**

- 基于Pydantic的严格参数验证
- 支持扫描参数、输出参数、雷达位置等完整配置
- 参数范围验证和类型检查
- 二进制数据打包和解包功能

---

## **3. 数据处理与存储架构**

### **3.1 数据类型与处理流程**

**支持的数据类型：**

- **散射图像数据**：幅度和相位的复数图像，支持极坐标到笛卡尔坐标转换
- **形变数据**：地面形变监测结果，单精度浮点数格式
- **置信度数据**：数据质量评估指标
- **动目标数据**：移动目标检测结果，包含目标编号、角度、距离、速度
- **雷达信息数据**：设备状态、配置参数等元数据
- **日志文件**：设备运行日志和错误记录

**数据处理管道：**

1. **接收阶段**：TCP连接接收二进制数据包
2. **验证阶段**：MD5校验确保数据完整性
3. **解压阶段**：Snappy解压缩数据
4. **解析阶段**：按数据类型解析为结构化数据
5. **存储阶段**：保存到MongoDB和文件系统
6. **转换阶段**：图像格式转换和坐标变换（多进程处理）

### **3.2 数据库架构设计**

**分层数据库结构：**

- **base_data数据库**：系统级数据（用户、场景、雷达基础信息）
- **雷达专属数据库**：每个雷达设备独立数据库
- **集合命名规范**：按数据类型和任务ID组织

**数据存储策略：**

```
base_data/
├── users (用户信息)
├── scene (场景配置)
└── radar (雷达基础信息)

{radar_id}/
├── radar_information (设备详细信息)
├── scene_parameter (场景参数配置)
├── platform_command (平台命令记录)
├── img_data_{mission_id} (散射图像数据)
├── deformation_data_{mission_id} (形变数据)
├── confidence_data_{mission_id} (置信度数据)
├── move_target_data_{mission_id} (动目标数据)
└── monitor_area_{mission_id} (监测区域定义)
```

### **3.3 文件系统组织**

**文件存储结构：**

```
{BASE_FILE_PATH}/
└── {radar_id}/
    ├── algorithm_file/ (算法文件)
    ├── log_file/ (日志文件)
    ├── work_data/ (工作数据)
    │   └── {mission_id}/
    │       ├── image_data/ (图像数据)
    │       │   ├── polar_{seq}.tiff (极坐标图像)
    │       │   ├── magnitude_{seq}.tiff (幅度图像)
    │       │   ├── phase_{seq}.tiff (相位图像)
    │       │   └── cart_{seq}.png (笛卡尔坐标图像)
    │       ├── deformation_data/ (形变数据)
    │       └── confidence_data/ (置信度数据)
    └── radar_file/ (雷达配置文件)
```

**图像元数据管理：**

- TIFF格式存储，包含完整的测量参数
- 元数据包含：角度范围、距离范围、坐标信息
- 支持多层图像数据（幅度+相位）
- 自动生成可视化的笛卡尔坐标图像

### **3.4 数据验证与约束**

**参数验证框架：**

- 基于Pydantic的强类型验证
- 自定义验证装饰器和错误处理
- 统一的API响应格式
- 数据库操作异常处理

**关键约束规则：**

- 雷达ID全局唯一（8位十六进制）
- 场景坐标有效性验证
- 监测区域边界合理性检查
- 用户权限级联验证

---

## **4. 系统架构与技术实现**

### **4.1 多线程并发架构**

**主要线程组件：**

- **Flask Web服务器**：处理HTTP API请求
- **TCP雷达服务器**：管理雷达设备连接
- **雷达模拟器**：开发测试用的设备模拟
- **文件监控服务**：监控文件系统变化
- **数据处理工作线程**：异步处理雷达数据

**线程安全机制：**

- RadarManager单例模式，线程安全的设备管理
- 命令队列和事件同步避免竞争条件
- 线程安全的数据库连接池
- 分布式锁防止并发冲突

### **4.2 通信协议设计**

**TCP协议栈：**

```
应用层: 自定义二进制协议
传输层: TCP长连接 + 心跳保活
网络层: IPv4/IPv6支持
```

**消息格式：**

```c
struct MessageHeader {
    uint16_t header;        // 协议头 (0x5A5A/0x3C3C)
    uint32_t radar_id;      // 雷达ID
    uint8_t  command;       // 命令码
    uint8_t  extend;        // 扩展码
    uint8_t  sign;          // 标志位
    uint32_t counter;       // 序号
    uint8_t  state_code;    // 状态码
    uint8_t  reserved[10];  // 保留字段
    uint32_t data_len;      // 数据长度
};
```

### **4.3 命令处理机制**

**命令分类：**

- **设置命令**：场景参数配置、工作控制
- **查询命令**：参数查询、状态查询
- **上传命令**：数据主动上报
- **控制命令**：开机、关机、重启

**装饰器模式的命令处理：**

```python
@handle_radar_command(
    command=CommandCode.SETPARAMETER.value,
    extend=SetParamter.SCENE.value,
    info="设置雷达场景参数"
)
def set_scene_parameter(self, parameters: Dict[str, Any]) -> bytes:
    # 参数验证和处理逻辑
    pass
```

### **4.4 异常处理与容错机制**

**网络异常处理：**

- 连接超时和重试机制
- 数据传输中断恢复
- 心跳失败自动重连
- 网络质量监控和告警

**数据异常处理：**

- MD5校验失败处理
- 数据解压缩错误恢复
- 格式验证和类型转换
- 数据库操作异常回滚

**系统异常处理：**

- 统一的异常捕获和日志记录
- 优雅的服务降级机制
- 资源泄漏防护
- 进程崩溃自动重启

---

## **5. Web API接口设计**

### **5.1 API架构模式**

**蓝图模块化设计：**

- **用户管理模块** (`/user`)：登录、注册、密码管理
- **雷达管理模块** (`/radar_manage`)：设备管理、场景配置
- **数据分析模块** (`/data_analysis`)：数据查询、统计分析
- **雷达信息模块** (`/radar_information`)：设备状态、参数查询
- **场景参数模块** (`/scene_parameter`)：参数配置、更新

**统一响应格式：**

```json
{
  "status": "success|error",
  "message": "操作结果描述",
  "data": "具体数据内容",
  "code": "错误码（可选）"
}
```

### **5.2 核心API接口**

**用户认证接口：**

- `POST /user/login` - 用户登录
- `POST /user/register` - 用户注册
- `POST /user/change_password` - 修改密码

**雷达管理接口：**

- `POST /radar_manage/change_mission_name` - 任务重命名
- `POST /radar_manage/change_scene_name` - 场景重命名
- `POST /radar_manage/delete_mission` - 删除任务
- `POST /radar_manage/delete_radar` - 删除雷达
- `POST /radar_manage/change_scene_coordinates` - 场景坐标设置
- `POST /radar_manage/change_radar_coordinates` - 雷达坐标设置

**数据分析接口：**

- `POST /data_analysis/get_scene_coordinates` - 获取场景坐标
- `POST /data_analysis/add_monitor_area` - 添加监测区域
- `POST /data_analysis/delete_monitor_area` - 删除监测区域

**场景参数接口：**

- `POST /scene_parameter/check_scene_parameters` - 查询场景参数
- `POST /scene_parameter/update_scene_parameter` - 更新场景参数

### **5.3 请求验证框架**

**装饰器验证链：**

```python
@jwt_required()  # JWT认证
@handle_api_exceptions(info="操作描述")  # 异常处理
@handle_database_exceptions  # 数据库异常
@validate_request("field1", "field2")  # 字段验证
def api_function(**kwargs):
    # 业务逻辑
    pass
```

**验证功能特性：**

- 自动JSON格式验证
- 必需字段存在性检查
- 数据类型和格式验证
- 统一错误响应格式
- 数据库操作异常处理

### **5.4 性能优化策略**

**数据库优化：**

- MongoDB分片存储，按雷达ID分库
- 索引优化：设备ID、时间戳、状态字段
- 连接池管理和复用
- 查询结果缓存机制

**并发处理优化：**

- 多进程图像处理（polar2cart转换）
- 异步数据处理队列
- 线程安全的设备管理
- 非阻塞I/O操作

**文件系统优化：**

- 分层目录结构
- TIFF格式高效存储
- 图像压缩和元数据管理
- 自动清理和归档机制

---

## **6. 日志系统与监控**

### **6.1 分层日志架构**

**日志模块分类：**

- **API模块** (`arcweb.api`)：Flask应用和各个蓝图
- **雷达核心模块** (`arcweb.radar`)：雷达通信、数据处理
- **服务器模块** (`arcweb.server`)：雷达服务器功能
- **模拟器模块** (`arcweb.simulator`)：雷达模拟器
- **客户端模块** (`arcweb.client`)：雷达客户端
- **Worker模块** (`arcweb.worker`)：后台处理任务

**统一日志格式：**

```
时间戳 - 模块名 - 级别 - [功能标识] 消息内容
2025-08-12 18:29:23,812 - arcweb.api - INFO - [API] 用户登录请求
2025-08-12 18:29:23,813 - arcweb.radar - INFO - [雷达核心] 开始数据处理
```

### **6.2 安全机制**

**认证与授权：**

- BCrypt密码哈希加密，随机盐值生成
- JWT Token认证，支持过期和刷新
- 基于角色的访问控制（RBAC）
- API接口级别的权限验证

**数据安全：**

- 雷达通信数据MD5校验
- 敏感操作审计日志
- 数据库连接加密
- 文件系统访问控制

### **6.3 监控与告警**

**系统监控指标：**

- 雷达设备在线状态和连接数
- 数据处理吞吐量和延迟
- 数据库连接池使用率
- 文件系统存储使用情况
- API接口响应时间和错误率

**告警机制：**

- 设备离线超时告警
- 数据处理异常告警
- 系统资源使用率告警
- 数据库连接异常告警
- 文件存储空间不足告警

---

## **7. 部署与运维**

### **7.1 系统依赖**

**核心技术栈：**

- **后端框架**：Flask + Flask-JWT-Extended
- **数据库**：MongoDB（支持分片）
- **数据处理**：NumPy, SciPy, Pandas
- **图像处理**：matplotlib, tifffile
- **数据压缩**：Snappy
- **网络通信**：Socket (TCP)
- **并发处理**：Threading, Multiprocessing

**Python依赖包：**

```
Flask>=2.0.0
Flask-JWT-Extended>=4.0.0
Flask-CORS>=3.0.0
pymongo>=4.0.0
pydantic>=2.0.0
numpy>=1.20.0
scipy>=1.7.0
pandas>=1.3.0
matplotlib>=3.5.0
tifffile>=2021.0.0
python-snappy>=0.6.0
bcrypt>=3.2.0
python-dotenv>=0.19.0
```

### **7.2 环境配置**

**环境变量配置：**

```bash
# 数据库配置
MONGO_URI_BASE=mongodb://localhost:27017/
MONGO_DB_NAME=base_data

# JWT配置
JWT_SECRET_KEY=your-secret-key
JWT_ACCESS_TOKEN_EXPIRES=1  # 天数

# 文件路径配置
BASE_FILE_PATH=/path/to/radar/data
DOWNLOAD_BASE_DIR=/path/to/downloads

# 服务器配置
HOST=127.0.0.1
PORT=1030

# Flask配置
FLASK_ENV=development
SECRET_KEY=your-flask-secret
```

### **7.3 启动流程**

**服务启动顺序：**

1. **日志系统初始化**：配置分层日志架构
2. **数据库连接**：MongoDB连接和集合初始化
3. **Flask应用启动**：Web API服务启动
4. **TCP雷达服务器**：雷达设备通信服务启动
5. **雷达模拟器**：开发测试用模拟器启动
6. **文件监控服务**：文件系统变化监控启动

**启动命令：**

```bash
# 生产环境启动
python app.py

# 开发环境启动（带调试）
FLASK_ENV=development python app.py
```

### **7.4 运维监控**

**日志管理：**

- 日志文件自动轮转（按大小和时间）
- 分级日志记录（DEBUG, INFO, WARN, ERROR）
- 日志集中收集和分析
- 关键操作审计日志

**性能监控：**

- 雷达设备连接状态监控
- API接口响应时间统计
- 数据库查询性能分析
- 系统资源使用率监控

**故障处理：**

- 自动异常检测和告警
- 设备离线自动重连
- 数据处理失败重试机制
- 系统健康检查和自愈

---

## **8. 开发与测试**

### **8.1 开发环境搭建**

**环境要求：**

- Python 3.8+
- MongoDB 4.4+
- 操作系统：Windows/Linux/macOS

**快速启动：**

```bash
# 1. 克隆项目
git clone <repository-url>
cd arcweb-backend

# 2. 安装依赖
pip install -r requirements.txt

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件配置数据库等信息

# 4. 启动服务
python app.py
```

### **8.2 测试策略**

**单元测试：**

- 数据验证框架测试
- 雷达通信协议测试
- 数据库操作测试
- API接口功能测试

**集成测试：**

- 雷达设备连接测试
- 数据处理流程测试
- 用户认证授权测试
- 文件系统操作测试

**性能测试：**

- 多雷达并发连接测试
- 大数据量处理性能测试
- API接口压力测试
- 数据库查询性能测试

### **8.3 代码质量保证**

**代码规范：**

- Python PEP 8编码规范
- 类型注解和文档字符串
- 统一的错误处理模式
- 模块化和可测试性设计

**质量检查工具：**

- Black代码格式化
- Flake8代码检查
- MyPy类型检查
- Pytest单元测试框架

---

## **9. 业务流程说明**

### **9.1 雷达设备接入流程**

**设备注册过程：**

1. 雷达设备建立TCP连接到服务器端口1030
2. 设备发送首个数据包，包含设备ID信息
3. 系统解析设备ID，检查是否为新设备
4. 新设备自动注册：创建数据库记录、文件目录、导入配置
5. 设备状态更新为在线，开始接收指令和上报数据

**数据上报流程：**

1. 雷达设备按配置周期采集监测数据
2. 数据经过压缩和MD5校验后上传
3. 服务器接收数据并进行完整性验证
4. 数据解析、存储到数据库和文件系统
5. 图像数据自动进行坐标转换和可视化处理

### **9.2 监测任务管理流程**

**任务创建与配置：**

1. 用户通过Web界面配置场景参数
2. 系统验证参数有效性和设备兼容性
3. 参数下发到雷达设备进行配置
4. 雷达设备确认配置成功后开始工作
5. 系统自动创建任务记录和数据集合

**监测区域管理：**

1. 用户在地图界面定义监测区域边界
2. 系统验证区域坐标的合理性
3. 区域信息存储到对应任务的数据集合
4. 支持区域的动态添加、修改和删除
5. 区域数据用于后续的数据分析和报告生成

### **9.3 数据分析与可视化**

**实时数据处理：**

- 散射图像的极坐标到笛卡尔坐标转换
- 形变数据的时序分析和趋势预测
- 动目标检测结果的轨迹跟踪
- 数据质量评估和异常检测

**历史数据分析：**

- 按时间范围查询历史监测数据
- 多维度数据统计和聚合分析
- 监测区域的数据对比分析
- 生成监测报告和可视化图表

---

## **10. 总结与展望**

### **10.1 系统特色**

**技术创新点：**

- 自定义二进制通信协议，高效可靠
- 多进程并行图像处理，提升性能
- 分层日志架构，便于问题定位
- 基于Pydantic的严格数据验证
- MongoDB分片存储，支持海量数据

**业务价值：**

- 支持大规模雷达设备集群管理
- 实现地面形变的精确监测
- 提供完整的数据处理和分析能力
- 支持多用户协作和权限管理
- 具备良好的扩展性和可维护性

### **10.2 未来发展方向**

**功能扩展：**

- 增加更多数据分析算法
- 支持移动端应用开发
- 集成机器学习预测模型
- 增强数据可视化能力
- 支持多语言国际化

**技术优化：**

- 引入微服务架构
- 增加缓存层提升性能
- 支持容器化部署
- 增强系统监控和告警
- 优化数据库查询性能

这份设计文档全面描述了ArcSAR雷达监测系统的架构设计、技术实现和业务流程，为系统的开发、部署和维护提供了详细的指导。系统采用现代化的技术栈和设计模式，具备良好的可扩展性和可维护性，能够满足大规模雷达监测应用的需求。
