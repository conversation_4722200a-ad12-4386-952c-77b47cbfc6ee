# **数据库需求文档**  

**版本：1.1**  
**最后更新：2025年8月12日**  

---

### **1. 系统角色与权限划分**  

| **角色**     | **权限描述**                                                 |
| ------------ | ------------------------------------------------------------ |
| **管理员**   | - **全局支配权**：所有数据、账号、场景绑定的最高控制权。<br>- **账号管理**：创建/删除/禁用/启用用户（含操作员、普通用户），修改角色或绑定场景。<br>- **权限调整**：直接升降级用户角色（无需授权）。 |
| **操作员**   | - **绑定多场景**：可管理多个绑定场景的完整权限（配置雷达、任务、区域）。<br>- **互斥性规则**：每个场景仅能被**一个操作员**绑定。<br>- **非绑定场景**：仅可查看数据（禁止修改）。 |
| **普通用户** | - **绑定多场景**：经管理员审批后可绑定多个场景。<br>- **数据访问**：仅可读写绑定场景的数据（权限由管理员细粒度控制，如仅读/部分写入）。<br>- **权限隔离**：无法操作其他用户账号或未授权场景。 |

> **补充权限继承**：  
>
> - 操作员对绑定场景下的雷达、任务、监测区域自动拥有完整管理权限。  
> - 普通用户权限严格限定于绑定场景内，且不继承子对象权限。  

---

### **2. 场景与数据绑定规则**  

#### **2.1 场景绑定唯一性**  

- 每个场景仅允许绑定**一个操作员**（互斥性）。  
- 管理员可强制解除绑定并重新分配场景。  
- **冲突处理流程**：管理员需先解除原操作员绑定，再绑定新操作员；此间场景状态标记为 **“待分配”**。  

#### **2.2 数据依赖关系**  

| **对象**     | **绑定规则**                                                 | **状态管理**                                    |
| ------------ | ------------------------------------------------------------ | ----------------------------------------------- |
| **雷达设备** | - 动态注册后状态为 **“未分配”**。<br>- 管理员手动分配至场景后状态为 **“已激活”**。<br>- 仅管理员和绑定操作员可管理。 | 初始状态：`未分配` → 分配后：`已激活/维护/故障` |
| **任务**     | - 必须绑定到某雷达和场景。<br>- 仅当雷达挂载至该场景时可运行。 | 状态：`待启动/运行中/暂停/已完成/已取消`        |
| **监测区域** | - 必须绑定到某任务。<br>- 仅任务所属场景的管理员或绑定操作员可修改。 | 状态随任务联动变更                              |

> **补充级联规则**：  
>
> - 删除**场景** → 自动解除所有用户绑定，关联雷达状态置为 **“未分配”**，任务及监测区域**逻辑删除**（标记 `is_deleted=1`）。  
> - 删除**雷达** → 关联任务及监测区域**逻辑删除**。  
> - 删除**用户** → 自动解除其所有场景绑定。  

---

### **3. 权限隔离与访问控制**  

#### **3.1 用户间权限隔离**  

- **管理员**：可干预所有账号状态（删除/禁用/修改角色）。  
- **操作员 & 普通用户**：  
  - 无法相互影响账号状态。  
  - 操作员仅能管理自身绑定场景，对其他场景**仅读**。  
- **普通用户权限粒度**：  
  - **部分写入示例**：在绑定场景内创建报告、修改非核心任务参数（需管理员授权）。  

#### **3.2 数据访问层级**  

| **角色** | **可访问数据范围**                                   |
| -------- | ---------------------------------------------------- |
| 管理员   | 所有场景、雷达、任务、监测区域及用户数据（全权限）。 |
| 操作员   | 绑定场景的全权限；非绑定场景仅读。                   |
| 普通用户 | 绑定场景内的数据（读写权限由管理员按需分配）。       |

---

### **4. 账号与对象生命周期管理**  

#### **4.1 创建与删除**  

- **管理员**：可随时创建/删除账号（操作员、普通用户）。  
- **用户**：不可自建或删除账号。  
- **删除逻辑**：所有删除操作均为**逻辑删除**（标记 `is_deleted=1`），审计日志记录完整操作轨迹。  

#### **4.2 权限与绑定变更**  

- 仅管理员可调整用户角色、绑定场景或敏感信息（如权限等级）。  
- 用户仅可修改非敏感信息（密码、昵称）。  

#### **4.3 默认状态规则**  

| **对象**   | **初始状态** | **状态变更权限**                      |
| ---------- | ------------ | ------------------------------------- |
| 新用户账号 | `启用`       | 管理员可禁用/启用                     |
| 新场景     | `激活`       | 管理员或绑定操作员可改为`维护`/`停用` |
| 雷达注册   | `未分配`     | 管理员分配后置为`已激活`              |

---

### **5. 审计日志与安全机制**  

#### **5.1 审计日志要求**  

- **记录范围**：  
  - 关键操作：账号变更、角色升降、场景绑定/解绑、雷达分配、数据删除。  
  - 字段：操作者ID、时间、操作类型、对象ID、操作前状态、操作后状态。  
- **访问权限**：仅管理员可查询审计日志。  
- **保留策略**：日志至少保留 **180天**。  

#### **5.2 敏感操作保护**  

- **二次确认**：管理员执行以下操作时需输入安全密码确认：  
  - 批量删除用户/场景  
  - 强制解除操作员绑定  
  - 权限角色批量变更  

---

### **6. 核心逻辑总结**  

| **规则**           | **说明**                                                     |
| ------------------ | ------------------------------------------------------------ |
| 管理员绝对控制     | 所有权限分配、绑定关系、状态变更需管理员操作。               |
| 操作员场景互斥绑定 | 一场景一操作员，但一操作员可绑多场景。                       |
| 雷达动态注册       | 自动注册后状态为`未分配`，需管理员手动分配至场景。           |
| 数据级联逻辑删除   | 删除父对象（如场景/雷达）时，子对象（任务/监测区域）标记`is_deleted=1`。 |
| 权限最小化原则     | 普通用户仅限绑定场景内细粒度授权操作。                       |

---

### **7. 补充说明**  

- **互斥性与责任分离**：单场景单操作员绑定避免管理冲突。  

- **安全与灵活性平衡**：  

  - 管理员动态调整绑定满足业务变动。  
  - 逻辑删除 + 审计日志保障数据可追溯性。  

- **动态注册雷达字段**：  

  ```plaintext
  雷达注册必填信息：SN（唯一标识）、型号、IP地址、固件版本、注册时间、初始状态（未分配）。
  ```

---

**此需求文档已闭合所有核心场景，覆盖权限模型、数据生命周期、审计与安全机制，可直接作为技术设计依据。**