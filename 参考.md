**版本：2.0** **最后更新：2025年8月12日**

---

## **1. 系统角色与权限划分**

### **1.1 基础角色定义**

| **角色**       | **权限描述**                                                                                                                                               | **数量限制** |
| -------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------ |
| **超级管理员** | - 系统最高权限，包含所有管理员权限 - 可创建/删除管理员账号 - 系统配置参数修改权限                                                                                | 建议不超过2个      |
| **管理员**     | - 全局支配权：所有数据、账号、场景绑定的最高控制权 - 账号管理：创建/删除/禁用/启用用户（含操作员、普通用户） - 权限调整：直接升降级用户角色 - 系统监控和运维权限 | 建议3-5个          |
| **操作员**     | - 绑定多场景：可管理多个绑定场景的完整权限 - 互斥性规则：每个场景仅能被一个操作员绑定 - 非绑定场景：仅可查看数据（禁止修改） - 可创建和管理场景内的工作报告      | 无数量限制         |
| **普通用户**   | - 绑定多场景：经管理员审批后可绑定多个场景 - 数据访问：仅可读写绑定场景的数据 - 权限隔离：无法操作其他用户账号或未授权场景 - 可参与协作任务和数据标注            | 无数量限制         |

### **1.2 权限继承与细分**

**操作员权限细分：**

- **设备管理权限**：可配置、启停、维护绑定场景内的雷达设备
- **任务管理权限**：创建、修改、删除、启停监测任务
- **区域管理权限**：划定、调整、删除监测区域边界
- **数据分析权限**：查看、导出、分析场景内所有历史数据
- **报告生成权限**：创建、编辑、发布场景监测报告
- **用户协作权限**：可邀请普通用户参与场景内协作（需管理员最终审批）

**普通用户权限细分：**

- **基础查看权限**：查看绑定场景的基础监测数据和设备状态
- **有限编辑权限**：修改个人创建的标注和备注信息
- **协作参与权限**：参与指定的数据标注、验证任务
- **报告查阅权限**：查看已发布的场景监测报告
- **数据下载权限**：下载授权范围内的数据文件（可按数据类型、时间范围限制）

---

## **2. 场景与数据绑定规则**

### **2.1 场景生命周期管理**

**场景创建流程：**

- 仅管理员可创建新场景
- 创建时必须指定场景名称、地理位置、监测目标、预期监测周期
- 新场景初始状态为"待配置"，需要绑定操作员后才能激活
- 每个场景需要设定监测数据保留周期（默认24个月）

**场景状态流转：**

- **待配置** → **已激活**：分配操作员并完成基础配置后
- **已激活** → **维护中**：主动维护或设备故障时
- **维护中** → **已激活**：维护完成后
- **已激活/维护中** → **已停用**：场景监测结束时
- **已停用** → **已归档**：数据迁移到冷存储后

### **2.2 设备动态注册与分配**

**雷达设备注册流程：**

- 新设备上线时自动向系统发送注册请求
- 系统记录设备唯一标识、型号、技术参数、网络信息
- 设备初始状态为"未分配"，等待管理员手动分配
- 设备分配时需要进行兼容性检查（设备能力与场景需求匹配）

**设备状态管理：**

- **未分配**：新注册设备的初始状态
- **配置中**：正在进行参数配置和调试
- **已激活**：正常工作状态，可接收和执行监测任务
- **维护中**：计划性维护或非关键故障处理
- **故障中**：关键故障导致无法正常工作
- **已下线**：永久停用或物理移除

**设备故障处理机制：**

- 设备离线超过5分钟自动标记为"通信异常"
- 连续3次心跳失败标记为"故障中"
- 故障设备的关联任务自动暂停，并通知绑定操作员
- 故障恢复后需要手动确认设备状态才能重新启动任务

### **2.3 任务与监测区域关联**

**任务创建约束：**

- 任务必须绑定到特定场景和雷达设备
- 监测区域必须在雷达设备的有效覆盖范围内
- 同一雷达设备可以执行多个任务，但在一次开机和关机的时间内只能运行一个项目
- 任务优先级机制：紧急任务可抢占普通任务的设备资源

**监测区域边界定义：**

- 支持多边形、圆形、扇形等几何形状
- 区域边界点坐标精度要求：GPS坐标小数点后6位
- 单个监测区域面积限制：最小1平方公里，最大100平方公里
- 支持区域分层监测：核心区、缓冲区、外围区的不同监测强度

---

## **3. 数据完整性与约束规则**

### **3.1 唯一性约束**

**用户账号唯一性：**

- 用户名全系统唯一，3-20个字符，支持字母、数字、下划线
- 邮箱地址全系统唯一，用于账号恢复和系统通知
- 手机号码可选，如提供则必须唯一且经过验证

**场景标识唯一性：**

- 场景编码采用地区代码+序号格式，如"BJ001"、"SH002"
- 场景名称在同一地区内唯一，不同地区可重名
- GPS坐标范围不能与已有场景重叠超过80%

**设备标识唯一性：**

- 设备序列号全系统唯一，由厂商提供
- 设备网络MAC地址唯一
- 同一场景内设备昵称不能重复

### **3.2 数据有效性约束**

**时间数据约束：**

- 所有时间戳采用UTC标准时间存储
- 任务开始时间不能早于当前时间
- 监测数据时间戳不能未来时间超过5分钟（容错机制）
- 用户最后登录时间定期更新，超过90天未登录的账号自动标记为"不活跃"

**数值数据约束：**

- 监测距离范围：0.1公里 - 50公里
- 监测精度等级：1-10级，数字越大精度越高
- 数据采集频率：1秒 - 3600秒之间
- 存储容量预警：单场景数据超过1TB时触发管理员通知

### **3.3 外键关系完整性**

**级联删除规则：**

- 删除场景时：关联的任务和监测区域标记为删除，但保留数据90天
- 删除用户时：其创建的任务转移给场景绑定操作员，个人标注数据保留但匿名化
- 删除雷达设备时：关联任务立即停止，历史数据保留但标记设备已下线

**引用完整性保护：**

- 任务运行期间不能删除关联的雷达设备
- 用户绑定场景期间不能直接删除用户账号，需先解除绑定
- 有活跃任务的监测区域不能删除，需先停止任务

---

## **4. 异常场景处理机制**

### **4.1 网络与通信异常**

**设备离线处理：**

- 设备心跳间隔：30秒一次状态报告
- 离线检测：连续3次心跳失败判定为离线
- 自动重连：设备离线后每5分钟尝试重连
- 数据缓存：设备本地缓存最近24小时数据，恢复连接后自动同步
- 告警机制：设备离线超过10分钟通知绑定操作员和管理员

**网络波动应对：**

- 数据传输超时重试：最多重试3次，间隔递增（5秒、15秒、45秒）
- 数据压缩传输：网络状况不佳时自动启用数据压缩
- 优先级传输：告警数据优先于常规监测数据传输
- 网络质量监控：实时监控网络延迟和丢包率

### **4.2 并发操作冲突**

**用户操作冲突：**

- 同一资源同时编辑时采用"最后写入胜出"策略
- 冲突检测：提交前检查资源最后修改时间戳
- 冲突提示：向用户展示冲突信息，允许手动合并或覆盖
- 操作锁定：关键操作（如设备配置）实施短期锁定（最长5分钟）

**数据一致性保障：**

- 事务性操作：关联数据的修改必须在同一事务中完成
- 分布式锁：多服务实例环境下使用分布式锁防止冲突
- 数据版本控制：重要配置数据保存历史版本
- 回滚机制：操作失败时自动回滚到上一稳定状态

### **4.3 业务异常处理**

**权限变更中断：**

- 操作员绑定解除时：正在执行的任务继续运行，但暂停新任务创建
- 用户权限降级时：立即收回高级权限，但保留只读访问
- 批量权限调整时：采用分批处理，单次失败不影响其他用户
- 权限变更日志：详细记录权限变更前后状态，支持紧急恢复

**数据恢复策略：**

- 逻辑删除恢复：管理员可在删除后30天内恢复数据
- 误删除保护：重要数据删除前需要二次确认
- 数据一致性检查：定期检查外键关系完整性
- 备份恢复：支持按时间点恢复，最小粒度为1小时

---

## **5. 性能与扩展性设计**

### **5.1 数据规模预估**

**用户数据规模：**

- 预计用户总数：1000-5000个（3年内）
- 同时在线用户：50-200个
- 日活跃用户：200-800个
- 用户操作日志：每用户每天50-100条操作记录

**业务数据规模：**

- 场景数量：100-500个
- 雷达设备数量：500-2000台
- 并发监测任务：1000-5000个
- 监测数据量：每天500GB-2TB新增数据
- 历史数据保留：24个月热数据，之后迁移冷存储

**系统性能指标：**

- 用户登录响应时间：< 2秒
- 数据查询响应时间：< 5秒（单次查询）
- 实时数据延迟：< 10秒
- 系统可用性：99.5%以上

### **5.2 查询性能优化**

**高频查询场景：**

- 用户权限验证查询：每次操作都需验证
- 设备状态查询：每30秒更新一次
- 实时监测数据查询：持续高频访问
- 历史数据统计查询：按日、周、月维度聚合

**索引策略原则：**

- 主键索引：所有表必须有自增主键
- 外键索引：外键字段自动创建索引
- 查询索引：基于查询频率和条件创建组合索引
- 时间索引：时间戳字段创建索引支持范围查询
- 状态索引：枚举状态字段创建索引

### **5.3 扩展性架构考虑**

**水平扩展策略：**

- 读写分离：查询操作使用读库集群，写操作使用主库
- 分库分表：按场景或地理区域进行数据分片
- 缓存策略：热点数据使用Redis缓存，过期时间根据数据更新频率设定
- CDN加速：静态资源和历史报告使用CDN分发

**垂直扩展预留：**

- 服务器资源监控：CPU、内存、磁盘使用率告警
- 数据库连接池：动态调整连接池大小
- 存储扩展：支持在线扩容，不影响业务连续性
- 计算资源弹性：根据负载自动调整服务实例数量

---

## **6. 安全与审计增强**

### **6.1 数据安全保护**

**敏感数据加密：**

- 用户密码使用BCrypt哈希加密，盐值随机生成
- 个人身份信息（手机号、邮箱）使用AES-256加密存储
- 设备通信数据使用TLS 1.3加密传输
- 数据库备份文件使用独立密钥加密

**访问控制强化：**

- 登录失败锁定：5次失败后锁定账号30分钟
- 会话管理：单用户最多允许3个并发会话
- IP白名单：管理员账号支持IP地址限制
- 操作超时：敏感操作30分钟无活动自动登出

### **6.2 审计日志详化**

**日志记录范围：**

- **用户行为日志**：登录/登出、权限使用、数据访问、配置修改
- **系统操作日志**：设备状态变更、任务启停、系统配置修改
- **数据变更日志**：数据创建、修改、删除的前后对比
- **安全事件日志**：登录失败、权限越界尝试、异常操作检测

**日志存储策略：**

- 日志分级：DEBUG、INFO、WARN、ERROR四个级别
- 日志轮转：按日期和大小进行日志文件轮转
- 日志备份：重要日志异地备份，保留3年
- 日志检索：支持多维度日志查询和统计分析

### **6.3 合规性要求**

**数据保护合规：**

- 个人信息保护：支持用户数据删除请求（被遗忘权）
- 数据出境管理：跨境数据传输需要审批和加密
- 审计要求：满足行业监管部门的审计要求
- 数据备份：关键数据异地备份，满足容灾要求

**操作追溯能力：**

- 数据血缘追踪：记录数据的产生、流转、使用全过程
- 操作回放：关键操作支持步骤回放和结果验证
- 责任定位：每个数据变更都能追溯到具体操作人员
- 证据保全：重要操作的数字签名和时间戳证明

---

## **7. 系统集成与接口设计**

### **7.1 外部系统对接**

**雷达设备接入协议：**

- 支持标准MQTT协议进行设备通信
- 设备数据格式：JSON结构化数据
- 心跳机制：设备定期发送状态信息
- 固件更新：支持远程固件升级和配置下发
- 协议版本管理：支持多版本协议兼容

**第三方系统集成：**

- GIS地图系统：集成主流地图服务API
- 天气数据服务：获取实时天气信息影响监测精度
- 消息推送服务：支持短信、邮件、移动推送通知
- 单点登录：支持LDAP、OAuth2等企业身份认证系统

### **7.2 API接口权限控制**

**接口认证机制：**

- JWT Token认证：用户登录后颁发访问令牌
- API Key管理：外部系统访问使用独立API密钥
- 接口限流：按用户级别设置不同的调用频率限制
- 签名验证：敏感接口使用数字签名验证

**权限矩阵设计：**

- 接口按功能模块分组：用户管理、设备管理、数据查询等
- 权限按角色预设：不同角色默认可访问的接口集合
- 动态权限控制：管理员可动态调整用户的接口访问权限
- 接口使用监控：记录接口调用频率和响应状态

---

## **8. 运维监控体系**

### **8.1 系统健康监控**

**关键指标监控：**

- **系统资源**：CPU使用率、内存占用、磁盘空间、网络带宽
- **数据库性能**：连接数、查询响应时间、慢查询统计
- **业务指标**：在线用户数、设备在线率、任务成功率、数据处理延迟
- **错误率监控**：API请求错误率、设备通信失败率、数据处理异常率

**告警机制：**

- 阈值告警：超过预设阈值时自动发送告警
- 趋势预警：基于历史数据预测潜在问题
- 告警级别：紧急、重要、一般三个级别
- 告警通道：邮件、短信、即时通讯工具多渠道通知
- 告警收敛：避免重复告警，相同问题在修复前不重复发送

### **8.2 运维自动化**

**自动化运维任务：**

- 数据备份：每日自动备份数据库和重要文件
- 日志清理：自动清理过期日志文件，释放存储空间
- 系统巡检：定期检查系统配置和服务状态
- 性能优化：自动分析并优化数据库查询性能
- 安全扫描：定期执行安全漏洞扫描和检测

**故障自动恢复：**

- 服务重启：检测到服务异常时自动重启
- 流量切换：主服务故障时自动切换到备用服务
- 数据同步：主从数据库自动同步和切换
- 资源扩展：负载过高时自动申请更多计算资源

---

## **9. 业务流程补充说明**

### **9.1 用户生命周期管理**

**用户入职流程：**

1. 管理员创建用户账号，设置初始密码和角色
2. 系统发送邮件通知用户激活账号
3. 用户首次登录强制修改密码，完善个人信息
4. 管理员根据工作需要分配场景绑定权限
5. 用户完成系统使用培训后正式启用账号

**用户离职流程：**

1. 管理员接到离职通知后立即禁用账号
2. 解除用户所有场景绑定，权限转移给接替人员
3. 导出用户相关工作数据，交接给业务部门
4. 用户个人数据匿名化处理，保留操作日志
5. 账号标记为已删除，但数据保留180天用于审计

### **9.2 设备生命周期管理**

**设备上线流程：**

1. 设备完成硬件安装和网络配置
2. 设备向系统发送注册请求，提供设备信息
3. 管理员验证设备合法性，完成设备注册
4. 分配设备到指定场景，配置监测参数
5. 执行设备功能测试，确认正常工作后激活

**设备下线流程：**

1. 停止设备上所有正在执行的监测任务
2. 导出设备历史数据，备份到归档系统
3. 解除设备与场景的绑定关系
4. 设备状态标记为已下线，保留配置信息
5. 物理移除设备，更新资产管理系统

### **9.3 场景运维流程**

**场景维护流程：**

1. 操作员申请场景维护，说明维护原因和预期时间
2. 管理员审批维护申请，通知相关用户
3. 暂停场景内所有监测任务，设备进入维护模式
4. 执行维护操作：设备检修、参数调优、区域调整等
5. 维护完成后测试验证，确认无误后恢复正常运行

**数据质量管理：**

1. 定期检查监测数据的完整性和准确性
2. 识别异常数据：缺失、重复、超出合理范围等
3. 数据清洗：修正可恢复的错误数据
4. 数据标记：对无法修复的异常数据进行标记
5. 质量报告：生成数据质量分析报告，指导业务优化

---

## **10. 总结与实施建议**

### **10.1 实施优先级**

**第一阶段（核心功能）：**

- 用户账号和权限管理系统
- 场景和设备基础管理功能
- 基本的监测任务创建和执行
- 核心数据存储和查询功能
- 基础安全机制和审计日志

**第二阶段（功能完善）：**

- 高级权限控制和细粒度授权
- 设备状态监控和故障处理
- 数据质量管理和异常检测
- 系统性能优化和扩展性改进
- 外部系统集成和API接口

**第三阶段（高级特性）：**

- 智能化运维和自动化处理
- 高级数据分析和报告功能
- 移动端应用和消息推送
- 多地域部署和灾备系统
- 人工智能辅助决策功能

### **10.2 风险控制建议**

**技术风险：**

- 制定详细的技术选型方案，避免技术债务
- 建立完整的测试体系，确保系统稳定性
- 预留充足的性能余量，应对业务快速增长
- 建立代码审查和质量控制流程

**业务风险：**

- 与业务部门密切沟通，确保需求理解准确
- 分阶段交付，及时获得用户反馈和验证
- 建立变更管理流程，控制需求变更影响
- 制定应急预案，应对系统故障和数据丢失

**合规风险：**

- 深入了解行业监管要求，确保系统合规
- 建立完善的数据保护机制，保护用户隐私
- 定期进行安全评估和漏洞扫描
- 建立与监管部门的沟通机制

这份完整的需求文档涵盖了数据库设计的各个方面，为系统开发提供了详细的指导。在实际实施过程中，建议根据具体的业务场景和技术条件进行适当调整和优化。
